package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__76;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__76.class,
    uses = {VideoMappingUtils.class,VideoDetailVoToVideoMapper.class},
    imports = {}
)
public interface VideoToVideoDetailVoMapper extends BaseMapper<Video, VideoDetailVo> {
}
