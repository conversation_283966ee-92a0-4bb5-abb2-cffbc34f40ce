package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {UserResumeToUserResumeVoMapper__3.class},
    imports = {}
)
public interface UserResumeVoToUserResumeMapper__3 extends BaseMapper<UserResumeVo, UserResume> {
}
