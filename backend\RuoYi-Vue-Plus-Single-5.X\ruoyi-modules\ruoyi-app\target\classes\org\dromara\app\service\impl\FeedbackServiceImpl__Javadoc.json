{"doc": "\n 意见反馈Service业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询意见反馈\r\n\r\n @param id 意见反馈主键\r\n @return 意见反馈\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询意见反馈列表\r\n\r\n @param bo        意见反馈\r\n @param pageQuery 分页查询条件\r\n @return 意见反馈分页列表\r\n"}, {"name": "selectUserFeedbackList", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户反馈列表\r\n\r\n @param userId 用户ID\r\n @return 反馈列表\r\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": "\n 构建查询条件\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": "\n 新增意见反馈\r\n\r\n @param bo 意见反馈\r\n @return 是否新增成功\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.FeedbackBo"], "doc": "\n 修改意见反馈\r\n\r\n @param bo 意见反馈\r\n @return 结果\r\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.Feedback"], "doc": "\n 保存前的数据校验\r\n\r\n @param entity 实体类数据\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 批量删除意见反馈\r\n\r\n @param ids     需要删除的意见反馈主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return 结果\r\n"}, {"name": "getUserFeedbackStats", "paramTypes": ["java.lang.Long"], "doc": "\n 获取用户反馈统计信息\r\n\r\n @param userId 用户ID\r\n @return 统计信息\r\n"}], "constructors": []}