package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {InterviewModeToInterviewModeVoMapper__3.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper__3 extends BaseMapper<InterviewModeVo, InterviewMode> {
}
