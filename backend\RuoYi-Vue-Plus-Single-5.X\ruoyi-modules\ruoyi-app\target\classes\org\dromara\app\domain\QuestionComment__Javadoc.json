{"doc": "\n 题目评论实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "commentId", "doc": "\n 评论ID\r\n"}, {"name": "questionId", "doc": "\n 题目ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "parentId", "doc": "\n 父评论ID（回复时使用）\r\n"}, {"name": "content", "doc": "\n 评论内容\r\n"}, {"name": "likeCount", "doc": "\n 点赞数\r\n"}, {"name": "replyCount", "doc": "\n 回复数\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1删除）\r\n"}, {"name": "sort", "doc": "\n 排序\r\n"}, {"name": "ip<PERSON><PERSON><PERSON>", "doc": "\n IP地址\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}