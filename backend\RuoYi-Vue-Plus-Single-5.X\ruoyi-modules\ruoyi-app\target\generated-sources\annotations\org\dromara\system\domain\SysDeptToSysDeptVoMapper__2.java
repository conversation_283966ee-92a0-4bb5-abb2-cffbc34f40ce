package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__2;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysDeptBoToSysDeptMapper__2.class,SysDeptVoToSysDeptMapper__2.class,SysDeptBoToSysDeptMapper__2.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__2 extends BaseMapper<SysDept, SysDeptVo> {
}
