package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper__3;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {VideoCommentBoToVideoCommentMapper__3.class,VideoCommentVoToVideoCommentMapper__3.class},
    imports = {}
)
public interface VideoCommentToVideoCommentVoMapper__3 extends BaseMapper<VideoComment, VideoCommentVo> {
}
