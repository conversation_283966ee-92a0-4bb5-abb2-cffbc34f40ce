package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {},
    imports = {}
)
public interface PaymentOrderBoToSysPaymentOrderMapper__2 extends BaseMapper<PaymentOrderBo, SysPaymentOrder> {
}
