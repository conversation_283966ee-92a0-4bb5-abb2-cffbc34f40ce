{"doc": "\n 题库实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "bankId", "doc": "\n 题库ID\r\n"}, {"name": "bankCode", "doc": "\n 题库编码\r\n"}, {"name": "title", "doc": "\n 题库标题\r\n"}, {"name": "description", "doc": "\n 题库描述\r\n"}, {"name": "majorId", "doc": "\n 专业ID\r\n"}, {"name": "icon", "doc": "\n 题库图标\r\n"}, {"name": "color", "doc": "\n 题库颜色\r\n"}, {"name": "difficulty", "doc": "\n 难度（1-简单 2-中等 3-困难）\r\n"}, {"name": "totalQuestions", "doc": "\n 题目总数\r\n"}, {"name": "practiceCount", "doc": "\n 练习次数\r\n"}, {"name": "categories", "doc": "\n 分类标签（JSON格式）\r\n"}, {"name": "sort", "doc": "\n 排序\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "isBookmarked", "doc": "\n 是否收藏（非数据库字段）\r\n"}, {"name": "progress", "doc": "\n 学习进度（非数据库字段）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}