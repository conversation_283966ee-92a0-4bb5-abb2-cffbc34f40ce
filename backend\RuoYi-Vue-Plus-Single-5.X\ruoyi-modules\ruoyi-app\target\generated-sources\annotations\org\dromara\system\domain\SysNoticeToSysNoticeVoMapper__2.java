package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__2;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysNoticeVoToSysNoticeMapper__2.class,SysNoticeBoToSysNoticeMapper__2.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__2 extends BaseMapper<SysNotice, SysNoticeVo> {
}
