package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.VideoComment;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {VideoCommentToVideoCommentVoMapper__3.class,VideoCommentToVideoCommentVoMapper__3.class},
    imports = {}
)
public interface VideoCommentVoToVideoCommentMapper__3 extends BaseMapper<VideoCommentVo, VideoComment> {
}
