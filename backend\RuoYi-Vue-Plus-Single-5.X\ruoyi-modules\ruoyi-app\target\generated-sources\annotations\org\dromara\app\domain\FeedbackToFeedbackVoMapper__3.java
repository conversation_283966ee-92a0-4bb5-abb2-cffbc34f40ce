package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper__3;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {FeedbackVoToFeedbackMapper__3.class,FeedbackBoToFeedbackMapper__3.class},
    imports = {}
)
public interface FeedbackToFeedbackVoMapper__3 extends BaseMapper<Feedback, FeedbackVo> {
}
