package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__2;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysClientVoToSysClientMapper__2.class,SysClientBoToSysClientMapper__2.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__2 extends BaseMapper<SysClient, SysClientVo> {
}
