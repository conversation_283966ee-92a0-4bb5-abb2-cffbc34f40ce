package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper__3;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {InterviewResultVoToInterviewResultMapper__3.class,InterviewResultBoToInterviewResultMapper__3.class},
    imports = {}
)
public interface InterviewResultToInterviewResultVoMapper__3 extends BaseMapper<InterviewResult, InterviewResultVo> {
}
