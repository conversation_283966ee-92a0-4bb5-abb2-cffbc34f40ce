package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper__3;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {KnowledgeBaseBoToKnowledgeBaseMapper__3.class,KnowledgeBaseVoToKnowledgeBaseMapper__3.class},
    imports = {}
)
public interface KnowledgeBaseToKnowledgeBaseVoMapper__3 extends BaseMapper<KnowledgeBase, KnowledgeBaseVo> {
}
