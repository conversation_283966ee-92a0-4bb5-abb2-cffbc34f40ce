package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper__3;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {KnowledgeDocumentBoToKnowledgeDocumentMapper__3.class,KnowledgeDocumentVoToKnowledgeDocumentMapper__3.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper__3 extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}
