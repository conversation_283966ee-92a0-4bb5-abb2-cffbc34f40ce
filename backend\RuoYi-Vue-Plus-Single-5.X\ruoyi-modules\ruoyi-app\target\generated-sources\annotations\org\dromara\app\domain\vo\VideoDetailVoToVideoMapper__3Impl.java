package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.Video;
import org.dromara.app.utils.VideoMappingUtils;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:39:54+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class VideoDetailVoToVideoMapper__3Impl implements VideoDetailVoToVideoMapper__3 {

    @Override
    public Video convert(VideoDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Video video = new Video();

        if ( arg0.getCreateTime() != null ) {
            video.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            video.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        video.setCategory( arg0.getCategory() );
        video.setCollectCount( arg0.getCollectCount() );
        video.setDescription( arg0.getDescription() );
        video.setDifficulty( arg0.getDifficulty() );
        video.setDuration( arg0.getDuration() );
        video.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        video.setId( arg0.getId() );
        video.setInstructor( arg0.getInstructor() );
        video.setInstructorAvatar( arg0.getInstructorAvatar() );
        video.setInstructorId( arg0.getInstructorId() );
        video.setLikeCount( arg0.getLikeCount() );
        video.setPrice( arg0.getPrice() );
        video.setPublishTime( arg0.getPublishTime() );
        video.setRating( arg0.getRating() );
        video.setShareCount( arg0.getShareCount() );
        video.setStudentCount( arg0.getStudentCount() );
        video.setTags( VideoMappingUtils.map( arg0.getTags() ) );
        video.setThumbnail( arg0.getThumbnail() );
        video.setTitle( arg0.getTitle() );
        video.setVideoUrl( arg0.getVideoUrl() );
        video.setViewCount( arg0.getViewCount() );

        return video;
    }

    @Override
    public Video convert(VideoDetailVo arg0, Video arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setCategory( arg0.getCategory() );
        arg1.setCollectCount( arg0.getCollectCount() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        arg1.setId( arg0.getId() );
        arg1.setInstructor( arg0.getInstructor() );
        arg1.setInstructorAvatar( arg0.getInstructorAvatar() );
        arg1.setInstructorId( arg0.getInstructorId() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setPublishTime( arg0.getPublishTime() );
        arg1.setRating( arg0.getRating() );
        arg1.setShareCount( arg0.getShareCount() );
        arg1.setStudentCount( arg0.getStudentCount() );
        arg1.setTags( VideoMappingUtils.map( arg0.getTags() ) );
        arg1.setThumbnail( arg0.getThumbnail() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setVideoUrl( arg0.getVideoUrl() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
