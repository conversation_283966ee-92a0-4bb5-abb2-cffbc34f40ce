package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:40:11+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KnowledgeDocumentToKnowledgeDocumentVoMapperImpl implements KnowledgeDocumentToKnowledgeDocumentVoMapper {

    @Override
    public KnowledgeDocumentVo convert(KnowledgeDocument arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocumentVo knowledgeDocumentVo = new KnowledgeDocumentVo();

        knowledgeDocumentVo.setContent( arg0.getContent() );
        knowledgeDocumentVo.setCreateBy( arg0.getCreateBy() );
        knowledgeDocumentVo.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeDocumentVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeDocumentVo.setDocType( arg0.getDocType() );
        knowledgeDocumentVo.setErrorMessage( arg0.getErrorMessage() );
        knowledgeDocumentVo.setFilePath( arg0.getFilePath() );
        knowledgeDocumentVo.setFileSize( arg0.getFileSize() );
        knowledgeDocumentVo.setId( arg0.getId() );
        knowledgeDocumentVo.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocumentVo.setLastProcessTime( arg0.getLastProcessTime() );
        knowledgeDocumentVo.setMetadata( arg0.getMetadata() );
        knowledgeDocumentVo.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocumentVo.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocumentVo.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocumentVo.setRemark( arg0.getRemark() );
        knowledgeDocumentVo.setSortOrder( arg0.getSortOrder() );
        knowledgeDocumentVo.setSource( arg0.getSource() );
        knowledgeDocumentVo.setStatus( arg0.getStatus() );
        knowledgeDocumentVo.setSummary( arg0.getSummary() );
        knowledgeDocumentVo.setTags( arg0.getTags() );
        knowledgeDocumentVo.setTitle( arg0.getTitle() );
        knowledgeDocumentVo.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeDocumentVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        knowledgeDocumentVo.setVectorCount( arg0.getVectorCount() );

        return knowledgeDocumentVo;
    }

    @Override
    public KnowledgeDocumentVo convert(KnowledgeDocument arg0, KnowledgeDocumentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setContent( arg0.getContent() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setDocType( arg0.getDocType() );
        arg1.setErrorMessage( arg0.getErrorMessage() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setLastProcessTime( arg0.getLastProcessTime() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setSource( arg0.getSource() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setVectorCount( arg0.getVectorCount() );

        return arg1;
    }
}
