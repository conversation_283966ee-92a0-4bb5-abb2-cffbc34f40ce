package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {FeedbackToFeedbackVoMapper__3.class},
    imports = {}
)
public interface FeedbackVoToFeedbackMapper__3 extends BaseMapper<FeedbackVo, Feedback> {
}
