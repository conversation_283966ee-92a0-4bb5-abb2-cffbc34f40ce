package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:40:11+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class VideoCommentToVideoCommentVoMapperImpl implements VideoCommentToVideoCommentVoMapper {

    @Override
    public VideoCommentVo convert(VideoComment arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoCommentVo videoCommentVo = new VideoCommentVo();

        videoCommentVo.setContent( arg0.getContent() );
        if ( arg0.getCreateTime() != null ) {
            videoCommentVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        videoCommentVo.setId( arg0.getId() );
        videoCommentVo.setLikeCount( arg0.getLikeCount() );
        videoCommentVo.setParentId( arg0.getParentId() );
        videoCommentVo.setUserId( arg0.getUserId() );
        videoCommentVo.setVideoId( arg0.getVideoId() );

        return videoCommentVo;
    }

    @Override
    public VideoCommentVo convert(VideoComment arg0, VideoCommentVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setContent( arg0.getContent() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setId( arg0.getId() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setVideoId( arg0.getVideoId() );

        return arg1;
    }
}
