package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper__3;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {UserResumeVoToUserResumeMapper__3.class,UserResumeBoToUserResumeMapper__3.class},
    imports = {}
)
public interface UserResumeToUserResumeVoMapper__3 extends BaseMapper<UserResume, UserResumeVo> {
}
