{"doc": "\n 题目实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "questionId", "doc": "\n 题目ID\r\n"}, {"name": "bankId", "doc": "\n 题库ID\r\n"}, {"name": "questionCode", "doc": "\n 题目编码\r\n"}, {"name": "title", "doc": "\n 题目标题\r\n"}, {"name": "description", "doc": "\n 题目描述\r\n"}, {"name": "content", "doc": "\n 题目内容\r\n"}, {"name": "answer", "doc": "\n 参考答案\r\n"}, {"name": "analysis", "doc": "\n 答案解析\r\n"}, {"name": "difficulty", "doc": "\n 难度（1-简单 2-中等 3-困难）\r\n"}, {"name": "category", "doc": "\n 分类\r\n"}, {"name": "type", "doc": "\n 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）\r\n"}, {"name": "practiceCount", "doc": "\n 练习次数\r\n"}, {"name": "correctRate", "doc": "\n 正确率\r\n"}, {"name": "acceptanceRate", "doc": "\n 通过率（百分比）\r\n"}, {"name": "commentCount", "doc": "\n 评论数\r\n"}, {"name": "tags", "doc": "\n 标签（JSON格式）\r\n"}, {"name": "sort", "doc": "\n 排序\r\n"}, {"name": "status", "doc": "\n 状态（0正常 1停用）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}