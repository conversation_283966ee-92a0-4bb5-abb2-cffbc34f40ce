package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__2;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysLogininforBoToSysLogininforMapper__2.class,SysLogininforVoToSysLogininforMapper__2.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__2 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
