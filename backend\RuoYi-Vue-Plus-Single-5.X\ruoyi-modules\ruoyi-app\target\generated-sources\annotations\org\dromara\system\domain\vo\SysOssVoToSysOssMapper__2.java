package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysOss;
import org.dromara.system.domain.SysOssToSysOssVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysOssToSysOssVoMapper__2.class},
    imports = {}
)
public interface SysOssVoToSysOssMapper__2 extends BaseMapper<SysOssVo, SysOss> {
}
