package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysSocialToSysSocialVoMapper__2.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__2 extends BaseMapper<SysSocialVo, SysSocial> {
}
