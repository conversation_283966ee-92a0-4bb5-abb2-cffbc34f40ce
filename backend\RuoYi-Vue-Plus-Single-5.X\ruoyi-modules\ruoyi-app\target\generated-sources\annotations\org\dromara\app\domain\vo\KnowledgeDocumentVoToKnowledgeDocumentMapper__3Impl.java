package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeDocument;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:39:49+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KnowledgeDocumentVoToKnowledgeDocumentMapper__3Impl implements KnowledgeDocumentVoToKnowledgeDocumentMapper__3 {

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeDocument knowledgeDocument = new KnowledgeDocument();

        knowledgeDocument.setCreateBy( arg0.getCreateBy() );
        knowledgeDocument.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeDocument.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeDocument.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeDocument.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeDocument.setContent( arg0.getContent() );
        knowledgeDocument.setDocType( arg0.getDocType() );
        knowledgeDocument.setErrorMessage( arg0.getErrorMessage() );
        knowledgeDocument.setFilePath( arg0.getFilePath() );
        knowledgeDocument.setFileSize( arg0.getFileSize() );
        knowledgeDocument.setId( arg0.getId() );
        knowledgeDocument.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        knowledgeDocument.setLastProcessTime( arg0.getLastProcessTime() );
        knowledgeDocument.setMetadata( arg0.getMetadata() );
        knowledgeDocument.setOriginalFilename( arg0.getOriginalFilename() );
        knowledgeDocument.setProcessConfig( arg0.getProcessConfig() );
        knowledgeDocument.setProcessStatus( arg0.getProcessStatus() );
        knowledgeDocument.setRemark( arg0.getRemark() );
        knowledgeDocument.setSortOrder( arg0.getSortOrder() );
        knowledgeDocument.setSource( arg0.getSource() );
        knowledgeDocument.setStatus( arg0.getStatus() );
        knowledgeDocument.setSummary( arg0.getSummary() );
        knowledgeDocument.setTags( arg0.getTags() );
        knowledgeDocument.setTitle( arg0.getTitle() );
        knowledgeDocument.setVectorCount( arg0.getVectorCount() );

        return knowledgeDocument;
    }

    @Override
    public KnowledgeDocument convert(KnowledgeDocumentVo arg0, KnowledgeDocument arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setContent( arg0.getContent() );
        arg1.setDocType( arg0.getDocType() );
        arg1.setErrorMessage( arg0.getErrorMessage() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setId( arg0.getId() );
        arg1.setKnowledgeBaseId( arg0.getKnowledgeBaseId() );
        arg1.setLastProcessTime( arg0.getLastProcessTime() );
        arg1.setMetadata( arg0.getMetadata() );
        arg1.setOriginalFilename( arg0.getOriginalFilename() );
        arg1.setProcessConfig( arg0.getProcessConfig() );
        arg1.setProcessStatus( arg0.getProcessStatus() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setSource( arg0.getSource() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSummary( arg0.getSummary() );
        arg1.setTags( arg0.getTags() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setVectorCount( arg0.getVectorCount() );

        return arg1;
    }
}
