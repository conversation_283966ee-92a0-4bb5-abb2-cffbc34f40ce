package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__2;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysMenuVoToSysMenuMapper__2.class,SysMenuBoToSysMenuMapper__2.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__2 extends BaseMapper<SysMenu, SysMenuVo> {
}
