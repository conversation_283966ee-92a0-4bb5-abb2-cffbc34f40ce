{"doc": "\n 成就管理Service接口\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询成就\r\n\r\n @param id 成就主键\r\n @return 成就\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询成就列表\r\n\r\n @param pageQuery 分页查询条件\r\n @return 成就集合\r\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": "\n 查询成就列表\r\n\r\n @param achievementBo 成就查询条件\r\n @return 成就集合\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": "\n 新增成就\r\n\r\n @param achievementBo 成就\r\n @return 结果\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.AchievementBo"], "doc": "\n 修改成就\r\n\r\n @param achievementBo 成就\r\n @return 结果\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 校验并批量删除成就信息\r\n\r\n @param ids     需要删除的成就主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return 结果\r\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 批量更新成就状态\r\n\r\n @param ids      成就ID列表\r\n @param isActive 是否激活\r\n @return 操作结果\r\n"}, {"name": "copyAchievement", "paramTypes": ["java.lang.Long"], "doc": "\n 复制成就\r\n\r\n @param id 成就ID\r\n @return 操作结果\r\n"}, {"name": "previewTriggerCondition", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 预览成就触发条件\r\n\r\n @param triggerCondition 触发条件JSON\r\n @param userId           测试用户ID\r\n @return 预览结果\r\n"}, {"name": "testAchievementRule", "paramTypes": ["java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.Object"], "doc": "\n 测试成就规则\r\n\r\n @param id        成就ID\r\n @param userId    测试用户ID\r\n @param eventType 事件类型\r\n @param eventData 事件数据\r\n @return 测试结果\r\n"}, {"name": "getAchievementStats", "paramTypes": [], "doc": "\n 获取成就统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "importAchievement", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": "\n 导入成就数据\r\n\r\n @param achievementList 成就数据列表\r\n @param isUpdateSupport 是否支持更新\r\n @param operName        操作用户\r\n @return 导入结果\r\n"}, {"name": "exportAchievement", "paramTypes": ["java.util.List"], "doc": "\n 导出成就数据\r\n\r\n @param list 成就数据列表\r\n @return 导出结果\r\n"}], "constructors": []}