package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper__3;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {VideoMappingUtils.class,VideoDetailVoToVideoMapper__3.class},
    imports = {}
)
public interface VideoToVideoDetailVoMapper__3 extends BaseMapper<Video, VideoDetailVo> {
}
