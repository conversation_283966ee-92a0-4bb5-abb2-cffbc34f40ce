package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeBase;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T11:39:51+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class KnowledgeBaseBoToKnowledgeBaseMapper__3Impl implements KnowledgeBaseBoToKnowledgeBaseMapper__3 {

    @Override
    public KnowledgeBase convert(KnowledgeBaseBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBase knowledgeBase = new KnowledgeBase();

        knowledgeBase.setDescription( arg0.getDescription() );
        knowledgeBase.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBase.setId( arg0.getId() );
        knowledgeBase.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBase.setName( arg0.getName() );
        knowledgeBase.setRemark( arg0.getRemark() );
        knowledgeBase.setSortOrder( arg0.getSortOrder() );
        knowledgeBase.setStatus( arg0.getStatus() );
        knowledgeBase.setType( arg0.getType() );
        knowledgeBase.setVectorDimension( arg0.getVectorDimension() );

        return knowledgeBase;
    }

    @Override
    public KnowledgeBase convert(KnowledgeBaseBo arg0, KnowledgeBase arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDescription( arg0.getDescription() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setId( arg0.getId() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setName( arg0.getName() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setVectorDimension( arg0.getVectorDimension() );

        return arg1;
    }
}
