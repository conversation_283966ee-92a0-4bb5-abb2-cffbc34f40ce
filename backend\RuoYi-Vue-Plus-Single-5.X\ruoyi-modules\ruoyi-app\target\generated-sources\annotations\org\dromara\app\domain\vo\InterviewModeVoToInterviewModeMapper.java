package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__76;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewMode;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__76.class,
    uses = {InterviewModeToInterviewModeVoMapper.class},
    imports = {}
)
public interface InterviewModeVoToInterviewModeMapper extends BaseMapper<InterviewModeVo, InterviewMode> {
}
