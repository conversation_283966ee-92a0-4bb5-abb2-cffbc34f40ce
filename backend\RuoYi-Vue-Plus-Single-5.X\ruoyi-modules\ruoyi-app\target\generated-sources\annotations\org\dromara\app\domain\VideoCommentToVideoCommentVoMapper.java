package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__76;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper;
import org.dromara.app.domain.vo.VideoCommentVo;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__76.class,
    uses = {VideoCommentVoToVideoCommentMapper.class,VideoCommentBoToVideoCommentMapper.class},
    imports = {}
)
public interface VideoCommentToVideoCommentVoMapper extends BaseMapper<VideoComment, VideoCommentVo> {
}
