package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMajor;
import org.dromara.system.domain.SysMajorToMajorVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {SysMajorToMajorVoMapper__2.class},
    imports = {}
)
public interface MajorVoToSysMajorMapper__2 extends BaseMapper<MajorVo, SysMajor> {
}
