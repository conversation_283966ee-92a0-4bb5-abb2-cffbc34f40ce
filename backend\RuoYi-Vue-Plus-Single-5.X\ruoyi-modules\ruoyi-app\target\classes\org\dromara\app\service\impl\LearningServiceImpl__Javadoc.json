{"doc": "\n 学习资源Service实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": "\n 构建查询条件\r\n"}, {"name": "convertToVo", "paramTypes": ["org.dromara.app.domain.QuestionBank", "java.lang.Long"], "doc": "\n 转换为VO对象\r\n"}, {"name": "convertQuestionToVo", "paramTypes": ["org.dromara.app.domain.Question"], "doc": "\n 转换题目为VO对象\r\n\r\n @param question 题目实体\r\n @return 题目VO\r\n"}, {"name": "convertCommentToVO", "paramTypes": ["org.dromara.app.domain.QuestionComment", "java.lang.Long"], "doc": "\n 转换评论实体为VO对象\r\n\r\n @param comment       评论实体\r\n @param currentUserId 当前用户ID\r\n @return 评论VO\r\n"}, {"name": "formatTimeDisplay", "paramTypes": ["java.util.Date"], "doc": "\n 格式化时间显示\r\n\r\n @param createTime 创建时间\r\n @return 格式化后的时间字符串\r\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": "\n 获取题库题目统计信息\r\n\r\n @param bankId 题库ID\r\n @return 统计信息\r\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": "\n 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\r\n\r\n @param queryDto 查询参数\r\n @param userId   用户ID\r\n @return 题库列表\r\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取专业题库统计信息\r\n\r\n @param majorId 专业ID\r\n @param userId  用户ID\r\n @return 统计信息\r\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": "\n 获取专业题库筛选选项计数\r\n\r\n @param majorId 专业ID\r\n @param userId  用户ID\r\n @return 筛选选项计数\r\n"}, {"name": "buildEnhancedQueryWrapper", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto", "java.lang.Long"], "doc": "\n 构建增强的查询条件\r\n\r\n @param queryDto 查询参数\r\n @param userId   用户ID\r\n @return 查询条件包装器\r\n"}, {"name": "applyEnhancedFilter", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper", "java.lang.String", "java.lang.Long"], "doc": "\n 应用增强的筛选条件\r\n\r\n @param wrapper 查询条件包装器\r\n @param filter  筛选条件\r\n @param userId  用户ID\r\n"}, {"name": "applySorting", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper", "java.lang.String"], "doc": "\n 应用排序条件\r\n\r\n @param wrapper  查询条件包装器\r\n @param sortType 排序类型\r\n"}, {"name": "calculateTotalStudyHours", "paramTypes": ["java.lang.Long"], "doc": "\n 计算用户总学习时长（小时）\r\n"}, {"name": "calculateCompletedCourses", "paramTypes": ["java.lang.Long"], "doc": "\n 计算用户完成的课程数\r\n"}, {"name": "calculateCurrentStreak", "paramTypes": ["java.lang.Long"], "doc": "\n 计算连续学习天数\r\n"}, {"name": "calculateWeeklyProgress", "paramTypes": ["java.lang.Long"], "doc": "\n 计算本周学习进度\r\n"}, {"name": "findRecommendedQuestionBank", "paramTypes": ["java.lang.Long"], "doc": "\n 查找推荐的题库\r\n"}, {"name": "calculateBankProgress", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": "\n 计算题库学习进度\r\n"}, {"name": "calculateBookCount", "paramTypes": ["java.lang.String"], "doc": "\n 计算书籍数量\r\n"}, {"name": "calculateVideoCount", "paramTypes": ["java.lang.String"], "doc": "\n 计算视频数量\r\n"}], "constructors": []}