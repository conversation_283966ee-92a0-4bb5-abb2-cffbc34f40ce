package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__72;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__72.class,
    uses = {JobCategoryToJobCategoryVoMapper__3.class},
    imports = {}
)
public interface JobCategoryVoToJobCategoryMapper__3 extends BaseMapper<JobCategoryVo, JobCategory> {
}
